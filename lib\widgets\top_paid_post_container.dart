import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:moneymouthy/services/post_service.dart';
import 'package:moneymouthy/services/user_service.dart';
import 'package:moneymouthy/services/profile_update_service.dart';
import 'package:moneymouthy/services/video_thumbnail_service.dart';
import 'package:moneymouthy/screens/media_player_screen.dart';
import 'package:moneymouthy/screens/image_gallery_screen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:image_network/image_network.dart';
import 'poll_widget.dart';

class TopPaidPostContainer extends StatefulWidget {
  final String category;
  final Post? topPost;
  final VoidCallback? onTap;

  const TopPaidPostContainer({
    super.key,
    required this.category,
    this.topPost,
    this.onTap,
  });

  @override
  State<TopPaidPostContainer> createState() => _TopPaidPostContainerState();
}

class _TopPaidPostContainerState extends State<TopPaidPostContainer>
    with SingleTickerProviderStateMixin {
  final UserService _userService = UserService();
  final ProfileUpdateService _profileUpdateService = ProfileUpdateService();
  Map<String, dynamic>? _userData;
  bool _isLoadingUserData = true;
  bool _isExpanded = true; // Track expansion state
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _expandAnimation;
  StreamSubscription<Map<String, dynamic>>? _profileUpdateSubscription;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _expandAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    // Don't auto-repeat for expand animation
    _loadUserData();

    // Listen to profile updates for real-time changes
    _profileUpdateSubscription =
        _profileUpdateService.profileDataUpdateStream.listen((updateData) {
      final userId = updateData['userId'] as String?;
      if (mounted && userId == widget.topPost?.authorId) {
        // Refresh user data when the post author's profile is updated
        _loadUserData();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _profileUpdateSubscription?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(TopPaidPostContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.topPost?.authorId != widget.topPost?.authorId) {
      _loadUserData();
    }
  }

  Future<void> _loadUserData() async {
    if (widget.topPost == null) {
      setState(() => _isLoadingUserData = false);
      return;
    }

    try {
      final userData = await _userService.getUserData(widget.topPost!.authorId);
      if (mounted) {
        setState(() {
          _userData = userData;
          _isLoadingUserData = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');
      if (mounted) {
        setState(() {
          _userData = null;
          _isLoadingUserData = false;
        });
      }
    }
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.topPost == null) {
      return const SizedBox.shrink();
    }

    final bool isPaidPost = widget.topPost!.price > 0;
    final bool hasAccess = widget.topPost!.isPaid || !isPaidPost;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      constraints: BoxConstraints(maxHeight: double.maxFinite),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF5159FF).withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: SingleChildScrollView(
            // Hide scroll indicators when expanded

            physics: _isExpanded
                ? const BouncingScrollPhysics()
                : const NeverScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header - Facebook-style user info
                _buildUserHeader(isPaidPost, hasAccess),
                AnimatedCrossFade(
                  duration: const Duration(milliseconds: 300),
                  crossFadeState: _isExpanded
                      ? CrossFadeState.showSecond
                      : CrossFadeState.showFirst,
                  firstChild: const SizedBox.shrink(),
                  secondChild: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Media content (images, videos, links)
                      _buildMediaContent(),
                    ],
                  ),
                ),
                // Post content
                _buildPostContent(hasAccess),
                // Poll content (if post has a poll)
                if (widget.topPost?.hasPoll == true)
                  PollWidget(
                    post: widget.topPost!,
                    isDetailView: false,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserHeader(bool isPaidPost, bool hasAccess) {
    final userProfileImage =
        _userData?['profileImageUrl'] ?? _userData?['photoUrl'];
    final userDisplayName = _userData?['name'] ?? widget.topPost!.author;

    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // User avatar
          _isLoadingUserData
              ? Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    shape: BoxShape.circle,
                  ),
                  child: const Center(
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                )
              : userProfileImage != null
                  ? CircleAvatar(
                      radius: 20,
                      backgroundColor: const Color(0xFF5159FF),
                      child: ClipOval(
                        child: kIsWeb
                            ? ImageNetwork(
                                image: userProfileImage,
                                height: 40,
                                width: 40,
                                duration: 500,
                                curve: Curves.easeIn,
                                onPointer: true,
                                debugPrint: false,
                                backgroundColor: Colors.white,
                                fitAndroidIos: BoxFit.cover,
                                fitWeb: BoxFitWeb.cover,
                                borderRadius: BorderRadius.circular(70),
                                onLoading: const CircularProgressIndicator(
                                  color: Colors.indigoAccent,
                                  strokeWidth: 0.1,
                                ),
                                onError: Text(
                                  userDisplayName.isNotEmpty
                                      ? userDisplayName[0].toUpperCase()
                                      : 'A',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              )
                            : Image.network(
                                userProfileImage,
                                height: 40,
                                width: 40,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Text(
                                    userDisplayName.isNotEmpty
                                        ? userDisplayName[0].toUpperCase()
                                        : 'A',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  );
                                },
                              ),
                      ),
                    )
                  : CircleAvatar(
                      radius: 20,
                      backgroundColor: const Color(0xFF5159FF),
                      child: Text(
                        userDisplayName.isNotEmpty
                            ? userDisplayName[0].toUpperCase()
                            : 'A',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),

          const SizedBox(width: 12),

          // User info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Display name and paid badge row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (isPaidPost) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color:
                              hasAccess ? Colors.green : Colors.amber.shade700,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'Top Paid Post',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                    // Price badge
                    if (isPaidPost)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: hasAccess
                              ? Colors.green
                              : const Color(0xFF5159FF),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              hasAccess
                                  ? Icons.check_circle
                                  : Icons.attach_money,
                              size: 14,
                              color: Colors.white,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              widget.topPost!.formattedPrice,
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      _isLoadingUserData ? 'Loading...' : userDisplayName,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 15,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),

                // Username and time
                Row(
                  children: [
                    if (_userData?['username'] != null) ...[
                      Text(
                        '@${_userData!['username']}',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      Text(
                        ' • ',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                    Text(
                      widget.topPost!.timeAgo,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostContent(bool hasAccess) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title (if exists)
        if (widget.topPost!.title != null && widget.topPost!.title!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            child: Text(
              widget.topPost!.title!,
              style: const TextStyle(
                fontSize: 17,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
                height: 1.3,
              ),
              // maxLines: _isExpanded ? null : 2,
              // overflow: _isExpanded ? null : TextOverflow.ellipsis,
            ),
          ),

        // Content text - show preview when collapsed, full content when expanded
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.topPost!.content,
                style: const TextStyle(
                    fontSize: 14, color: Colors.black87, height: 1.4),
              ),
              // Show "..." indicator when content is truncated
              if (!_isExpanded && widget.topPost!.content.length > 100)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    '...',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPlaceholderThumbnail() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1a1a1a),
            const Color(0xFF2d2d2d),
            const Color(0xFF1a1a1a),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background pattern
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.center,
                  radius: 1.0,
                  colors: [
                    Colors.white.withOpacity(0.05),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          // Video icon
          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.play_circle_filled,
                size: 40,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingThumbnail() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[300],
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    );
  }

  Widget _buildMediaContent() {
    final hasImages = widget.topPost!.imageUrls.isNotEmpty;
    final hasVideos = widget.topPost!.videoUrls.isNotEmpty;
    final hasLink =
        widget.topPost!.linkUrl != null && widget.topPost!.linkUrl!.isNotEmpty;

    if (!hasImages && !hasVideos && !hasLink) {
      return const SizedBox.shrink();
    }

    // Create a combined list of media items (videos first, then images)
    List<Widget> mediaItems = [];

    // Add videos first
    if (hasVideos) {
      for (int i = 0; i < widget.topPost!.videoUrls.length; i++) {
        mediaItems.add(_buildVideoItem(widget.topPost!.videoUrls[i], i));
      }
    }

    // Add images after videos
    if (hasImages) {
      for (int i = 0; i < widget.topPost!.imageUrls.length; i++) {
        mediaItems.add(_buildImageItem(widget.topPost!.imageUrls[i], i));
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Full-width horizontal scrollable media for top paid post
        if (mediaItems.isNotEmpty) ...[
          Container(
            height: 200, // Increased height for better visibility in top post
            margin: const EdgeInsets.symmetric(vertical: 8),
            child: PageView.builder(
              controller: PageController(
                  viewportFraction: 0.92), // Slight peek of next item
              itemCount: mediaItems.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: mediaItems[index],
                  ),
                );
              },
            ),
          ),

          // Media indicator dots if multiple items
          if (mediaItems.length > 1) ...[
            const SizedBox(height: 8),
            Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(
                  mediaItems.length,
                  (index) => Container(
                    width: 6,
                    height: 6,
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey[400],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ],

        // Link preview (if exists)
        if (hasLink) ...[
          const SizedBox(height: 6),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: _buildLinkPreview(),
          ),
        ],
      ],
    );
  }

  Widget _buildVideoItem(String videoUrl, int index) {
    return InkWell(
      onTap: () => _openMediaPlayer(initialIndex: index),
      child: Container(
        width: double.infinity, // Take full width
        height: 200, // Match the container height
        color: Colors.black,
        child: Stack(
          children: [
            // Video thumbnail
            Positioned.fill(child: _buildVideoThumbnailForUrl(videoUrl)),

            // Play button overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                ),
                child: const Center(
                  child: Icon(
                    Icons.play_circle_filled,
                    color: Colors.white,
                    size: 56, // Larger play button for better visibility
                  ),
                ),
              ),
            ),

            // Video badge (top left)
            Positioned(
              top: 12,
              left: 12,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Text(
                  'VIDEO',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

            // Video duration badge (bottom right)
            Positioned(
              bottom: 12,
              right: 12,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 3,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageItem(String imageUrl, int index) {
    return InkWell(
      onTap: () => _openImageGallery(index),
      child: SizedBox(
        width: double.infinity, // Take full width
        height: double.infinity, // Match the container height

        child: kIsWeb
            ? ImageNetwork(
                borderRadius: BorderRadius.circular(6),
                image: imageUrl,
                height: 200,
                width: 500,
                duration: 200,
                curve: Curves.easeIn,
                onPointer: false,
                debugPrint: false,
                backgroundColor: Colors.grey[100]!,
                fitAndroidIos: BoxFit.cover,
                fitWeb: BoxFitWeb.fill,
                onLoading: const Center(
                  child: CircularProgressIndicator(
                    color: Colors.indigoAccent,
                    strokeWidth: 1.0,
                  ),
                ),
                onError: Container(
                  height: 200,
                  color: Colors.grey[300],
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                      size: 48,
                    ),
                  ),
                ),
              )
            : Image.network(
                imageUrl,
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 200,
                    color: Colors.grey[300],
                    child: const Center(
                      child: Icon(
                        Icons.image_not_supported,
                        color: Colors.grey,
                        size: 48,
                      ),
                    ),
                  );
                },
              ),
      ),
    );
  }

  Widget _buildVideoThumbnailForUrl(String videoUrl) {
    if (kIsWeb) {
      // For web, show enhanced video placeholder
      return _buildWebVideoThumbnail(videoUrl);
    }

    return FutureBuilder<String?>(
      future: VideoThumbnailService.generateThumbnailFromUrl(videoUrl),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingThumbnail();
        }

        if (snapshot.hasData && snapshot.data != null) {
          return Stack(
            children: [
              // Actual video thumbnail
              Positioned.fill(
                child: Image.file(
                  File(snapshot.data!),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildPlaceholderThumbnail();
                  },
                ),
              ),
              // Video quality badge
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'HD',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          );
        }

        return _buildPlaceholderThumbnail();
      },
    );
  }

  Widget _buildWebVideoThumbnail(String videoUrl) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1a1a1a),
            const Color(0xFF2d2d2d),
            const Color(0xFF1a1a1a),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Video background
          Positioned.fill(
            child: Container(
              color: Colors.black87,
              child: const Center(
                child: Icon(Icons.videocam, size: 48, color: Colors.white54),
              ),
            ),
          ),
          // Video icon overlay
          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.play_circle_filled,
                size: 40,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ),
          // Video quality badge
          Positioned(
            top: 8,
            left: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'HD',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLinkPreview() {
    final linkUrl = widget.topPost!.linkUrl;
    if (linkUrl == null || linkUrl.isEmpty) return const SizedBox.shrink();

    return InkWell(
      onTap: () => _launchUrl(linkUrl),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.grey[50],
          border: Border.all(color: Colors.grey[300]!, width: 1),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF5159FF).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.link, color: Color(0xFF5159FF), size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'External Link',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    linkUrl.length > 50
                        ? '${linkUrl.substring(0, 50)}...'
                        : linkUrl,
                    style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            const Icon(Icons.open_in_new, color: Color(0xFF5159FF), size: 16),
          ],
        ),
      ),
    );
  }

  void _openMediaPlayer({int initialIndex = 0}) {
    if (widget.topPost?.videoUrls.isNotEmpty == true) {
      HapticFeedback.mediumImpact();
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MediaPlayerScreen(
            videoUrls: widget.topPost!.videoUrls,
            initialIndex: initialIndex,
            postTitle: widget.topPost!.title ??
                widget.topPost!.content.split('\n').first,
            authorName: widget.topPost!.author,
          ),
        ),
      );
    }
  }

  void _openImageGallery(int initialIndex) {
    if (widget.topPost?.imageUrls.isNotEmpty == true) {
      HapticFeedback.mediumImpact();
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ImageGalleryScreen(
            imageUrls: widget.topPost!.imageUrls,
            videoUrls: widget.topPost!.videoUrls,
            initialIndex: initialIndex,
            postTitle: widget.topPost!.title ??
                widget.topPost!.content.split('\n').first,
            authorName: widget.topPost!.author,
          ),
        ),
      );
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }
}
