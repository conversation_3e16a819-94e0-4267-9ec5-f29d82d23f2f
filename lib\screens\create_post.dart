import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:moneymouthy/controllers/category_controller.dart';
import '../controllers/wallet_controller.dart';
import '../controllers/post_controller.dart';
import '../controllers/draft_controller.dart';
import '../models/draft_model.dart';
import 'dart:io';
import '../services/post_amount_service.dart';
import '../services/serper_service.dart';
import '../services/video_thumbnail_service.dart';
import 'wallet_screen.dart';
import '../widgets/home/<USER>';

// Modify your image handling to support both local files and URLs
class ImageItem {
  final XFile? file;
  final String? url;
  final bool isNetwork;

  ImageItem.fromFile(this.file)
      : url = null,
        isNetwork = false;
  ImageItem.fromUrl(this.url)
      : file = null,
        isNetwork = true;
}

class CreatePostScreen extends StatefulWidget {
  const CreatePostScreen({super.key});

  @override
  State<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final _contentController = TextEditingController();
  late final WalletController walletController;
  late final PostController _postController;
  late final DraftController _draftController;
  final PostAmountService _postAmountService = PostAmountService();
  final CategoryController _categoryController = Get.find<CategoryController>();
  final SerperService _serperService = SerperService();

  double _postPrice = 0.05;
  bool _isLoading = false;
  bool _isSubmitting = false;

  // Media upload variables
  final List<ImageItem> _selectedImages = [];
  final List<XFile> _selectedVideos = [];
  final List<XFile> _selectedVideoAds = [];
  String? _linkUrl;
  final ImagePicker _imagePicker = ImagePicker();

  // Poll variables
  bool _hasPoll = false;

  // Image search variables
  final List<String> _searchedImageUrls = [];
  final List<String> _selectedSearchImageUrls = [];
  final TextEditingController _searchController = TextEditingController();
  bool _isSearchingImages = false;

  static const int _maxCharacters = 480;

  List<Map<String, dynamic>> get _categories => Categories.legacyFormat;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _loadAutoDraft();
    _postAmountService.postAmountStream.listen((amount) {
      if (mounted && _postPrice != amount) {
        setState(() {
          _postPrice = amount;
        });
      }
    });
  }

  Future<void> _initializeServices() async {
    try {
      walletController = Get.find<WalletController>();
      _postController = Get.find<PostController>();
      _draftController = Get.find<DraftController>();
      await walletController.initialize();
      await _loadSavedPostAmount();
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('Error initializing services in CreatePostScreen: $e');
    }
  }

  Future<void> _loadSavedPostAmount() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    try {
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (doc.exists && doc.data()!.containsKey('postAmount')) {
        final savedAmount = (doc.data()!['postAmount'] as num).toDouble();
        if (mounted) {
          setState(() {
            _postPrice = savedAmount;
          });
          _postAmountService.updatePostAmount(savedAmount);
        }
      }
    } catch (e) {
      debugPrint('Failed to load saved post amount: $e');
    }
  }

  Future<void> _loadAutoDraft() async {
    try {
      await _draftController.loadAutoDraft();
      final autoDraft = _draftController.autoDraft;
      if (autoDraft != null && mounted) {
        setState(() {
          _contentController.text = autoDraft.content;
          _postPrice = autoDraft.price;
          _linkUrl = autoDraft.linkUrl;
          _hasPoll = autoDraft.hasPoll;
        });
      }
    } catch (e) {
      debugPrint('Error loading auto-draft: $e');
    }
  }

  // Commenting out unused methods for now
  // Future<void> _saveDraft() async {
  //   try {
  //     final success = await _draftController.saveDraft(
  //       content: _contentController.text,
  //       price: _postPrice,
  //       category: _categoryController.selectedCategoryName,
  //       isPublic: true,
  //       allowComments: true,
  //       linkUrl: _linkUrl,
  //       images: _selectedImages.where((item) => item.file != null).map((item) => item.file!).toList(),
  //       videos: _selectedVideos,
  //     );
  //
  //     if (success && mounted) {
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         const SnackBar(
  //           content: Text('Draft saved successfully'),
  //           backgroundColor: Colors.green,
  //           duration: Duration(seconds: 2),
  //         ),
  //       );
  //     }
  //   } catch (e) {
  //     debugPrint('Error saving draft: $e');
  //     if (mounted) {
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         const SnackBar(
  //           content: Text('Failed to save draft'),
  //           backgroundColor: Colors.red,
  //           duration: Duration(seconds: 2),
  //         ),
  //       );
  //     }
  //   }
  // }

  // void _showDraftDialog() {
  //   showDialog(
  //     context: context,
  //     builder: (context) => Dialog(
  //       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
  //       child: Container(
  //         constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
  //         padding: const EdgeInsets.all(20),
  //         child: Column(
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 const Text(
  //                   'Drafts',
  //                   style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
  //                 ),
  //                 IconButton(
  //                   onPressed: () => Navigator.pop(context),
  //                   icon: const Icon(Icons.close),
  //                 ),
  //               ],
  //             ),
  //             const SizedBox(height: 16),
  //             Expanded(
  //               child: Obx(() {
  //                 if (_draftController.isLoading) {
  //                   return const Center(child: CircularProgressIndicator());
  //                 }
  //
  //                 if (!_draftController.hasDrafts) {
  //                   return const Center(
  //                     child: Column(
  //                       mainAxisAlignment: MainAxisAlignment.center,
  //                       children: [
  //                         Icon(Icons.drafts, size: 64, color: Colors.grey),
  //                         SizedBox(height: 16),
  //                         Text(
  //                           'No drafts saved',
  //                           style: TextStyle(fontSize: 16, color: Colors.grey),
  //                         ),
  //                       ],
  //                     ),
  //                   );
  //                 }
  //
  //                 return ListView.builder(
  //                   itemCount: _draftController.drafts.length,
  //                   itemBuilder: (context, index) {
  //                     final draft = _draftController.drafts[index];
  //                     return _buildDraftItem(draft);
  //                   },
  //                 );
  //               }),
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildDraftItem(DraftModel draft) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(
          draft.contentPreview,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Category: ${draft.category}'),
            Text('Price: \$${draft.price.toStringAsFixed(2)}'),
            Text(draft.formattedCreatedAt),
            if (draft.hasMedia)
              Text(
                '${draft.imagePaths.length} images, ${draft.videoPaths.length} videos',
                style: const TextStyle(color: Colors.blue),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _loadDraft(draft),
              icon: const Icon(Icons.edit),
              tooltip: 'Load Draft',
            ),
            IconButton(
              onPressed: () => _deleteDraft(draft.id),
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: 'Delete Draft',
            ),
          ],
        ),
      ),
    );
  }

  void _loadDraft(DraftModel draft) {
    setState(() {
      _contentController.text = draft.content;
      _postPrice = draft.price;
      _linkUrl = draft.linkUrl;
      _hasPoll = draft.hasPoll;
    });
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Draft loaded'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  Future<void> _deleteDraft(String draftId) async {
    final success = await _draftController.deleteDraft(draftId);
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Draft deleted'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  void dispose() {
    _draftController.stopAutoSave();
    _contentController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(ImageItem.fromFile(image));
        });
      }
    } catch (e) {
      _showErrorMessage('Failed to pick image: $e');
    }
  }

  Future<void> _takePhoto() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(ImageItem.fromFile(image));
        });
      }
    } catch (e) {
      _showErrorMessage('Failed to take photo: $e');
    }
  }

  Future<void> _addSelectedImages() async {
    if (_selectedSearchImageUrls.isEmpty) return;

    setState(() {
      for (var url in _selectedSearchImageUrls) {
        _selectedImages.add(ImageItem.fromUrl(url));
      }
    });

    Navigator.pop(context); // Close the bottom sheet

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            '${_selectedSearchImageUrls.length} image(s) added successfully'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );

    _selectedSearchImageUrls.clear();
  }

  Future<void> _pickVideo() async {
    try {
      if (_selectedVideos.isNotEmpty) {
        _showErrorMessage('You can only add 1 video per post');
        return;
      }

      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(seconds: 30),
      );

      if (video != null) {
        final isValid = await _validateVideoDuration(video, 30);
        if (isValid) {
          setState(() {
            _selectedVideos.add(video);
          });
        }
      }
    } catch (e) {
      _showErrorMessage('Failed to pick video: $e');
    }
  }

  Future<void> _recordVideo() async {
    try {
      if (_selectedVideos.isNotEmpty) {
        _showErrorMessage('You can only add 1 video per post');
        return;
      }

      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(seconds: 30),
      );

      if (video != null) {
        final isValid = await _validateVideoDuration(video, 30);
        if (isValid) {
          setState(() {
            _selectedVideos.add(video);
          });
        }
      }
    } catch (e) {
      _showErrorMessage('Failed to record video: $e');
    }
  }

  Future<void> _pickVideoAd() async {
    try {
      if (_selectedVideoAds.isNotEmpty) {
        _showErrorMessage('You can only add 1 video ad per post');
        return;
      }

      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(seconds: 60),
      );

      if (video != null) {
        final isValid = await _validateVideoDuration(video, 60);
        if (isValid) {
          setState(() {
            _selectedVideoAds.add(video);
            _updatePriceForVideoAd();
          });
        }
      }
    } catch (e) {
      _showErrorMessage('Failed to pick video ad: $e');
    }
  }

  Future<void> _recordVideoAd() async {
    try {
      if (_selectedVideoAds.isNotEmpty) {
        _showErrorMessage('You can only add 1 video ad per post');
        return;
      }

      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(seconds: 60),
      );

      if (video != null) {
        final isValid = await _validateVideoDuration(video, 60);
        if (isValid) {
          setState(() {
            _selectedVideoAds.add(video);
            _updatePriceForVideoAd();
          });
        }
      }
    } catch (e) {
      _showErrorMessage('Failed to record video ad: $e');
    }
  }

  void _updatePriceForVideoAd() {
    final selectedCategory = _categoryController.selectedCategoryName;
    final topPost = _postController.getTopPaidPostForCategory(selectedCategory);

    if (topPost != null) {
      final topPostPrice = topPost.price;
      final newPrice = topPostPrice + 1.0;
      setState(() {
        _postPrice = newPrice;
      });
      _postAmountService.updatePostAmount(newPrice);
    } else {
      final newPrice = 0.05 + 1.0;
      setState(() {
        _postPrice = newPrice;
      });
      _postAmountService.updatePostAmount(newPrice);
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
    });
  }

  void _removeVideoAd(int index) {
    setState(() {
      _selectedVideoAds.removeAt(index);
      _loadSavedPostAmount();
    });
  }

  Future<bool> _validateVideoDuration(XFile videoFile, int maxSeconds) async {
    try {
      final duration =
          await VideoThumbnailService.getVideoDurationFromXFile(videoFile);
      if (duration == null) {
        debugPrint('Could not determine video duration, allowing video');
        return true;
      }

      if (duration.inSeconds > maxSeconds) {
        final minutes = maxSeconds ~/ 60;
        final seconds = maxSeconds % 60;
        final timeLimit =
            minutes > 0 ? '${minutes}m ${seconds}s' : '${seconds}s';
        _showErrorMessage(
          'Video is too long. Maximum duration is $timeLimit. '
          'Your video is ${VideoThumbnailService.formatDuration(duration)}.',
        );
        return false;
      }
      return true;
    } catch (e) {
      debugPrint('Error validating video duration: $e');
      return true;
    }
  }

  void _showAddLinkDialog() {
    final TextEditingController linkController =
        TextEditingController(text: _linkUrl);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Link'),
        content: TextField(
          controller: linkController,
          decoration: const InputDecoration(
            hintText: 'Enter URL (https://...)',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.url,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final url = linkController.text.trim();
              if (url.isNotEmpty) {
                setState(() {
                  _linkUrl = url;
                });
              }
              Navigator.pop(context);
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _removeLink() {
    setState(() {
      _linkUrl = null;
    });
  }

  void _insertEmoji(String emoji) {
    final text = _contentController.text;
    final selection = _contentController.selection;
    final newText = text.replaceRange(selection.start, selection.end, emoji);

    if (newText.length <= _maxCharacters) {
      _contentController.text = newText;
      _contentController.selection =
          TextSelection.collapsed(offset: selection.start + emoji.length);
    }
  }

  Future<void> _showImageSearchBottomSheet() async {
    setState(() {
      _searchedImageUrls.clear();
      _selectedSearchImageUrls.clear();
      _searchController.clear();
      _isSearchingImages = false; // Reset search state
    });

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (BuildContext context, StateSetter setModalState) {
          return DraggableScrollableSheet(
            initialChildSize: 0.9,
            minChildSize: 0.5,
            maxChildSize: 0.9,
            expand: false,
            builder: (context, scrollController) => Column(
              children: [
                // Header with close button
                Container(
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(20)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      const Text(
                        'Search Images',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),

                // Search input
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search for images...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _isSearchingImages
                          ? const Padding(
                              padding: EdgeInsets.all(12.0),
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              ),
                            )
                          : IconButton(
                              icon: const Icon(Icons.search),
                              onPressed: () =>
                                  _performImageSearch(setModalState),
                            ),
                    ),
                    onSubmitted: (_) => _performImageSearch(setModalState),
                  ),
                ),

                // Results grid or empty state
                Expanded(
                  child: _isSearchingImages
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('Searching for images...'),
                            ],
                          ),
                        )
                      : _searchedImageUrls.isEmpty
                          ? const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.image_search,
                                      size: 64, color: Colors.grey),
                                  SizedBox(height: 16),
                                  Text(
                                    'Search for images to display results',
                                    style: TextStyle(
                                        fontSize: 16, color: Colors.grey),
                                  ),
                                ],
                              ),
                            )
                          : GridView.builder(
                              controller: scrollController,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              gridDelegate:
                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                crossAxisSpacing: 8,
                                mainAxisSpacing: 8,
                                childAspectRatio: 1.0,
                              ),
                              itemCount: _searchedImageUrls.length,
                              itemBuilder: (context, index) {
                                final url = _searchedImageUrls[index];
                                final isSelected =
                                    _selectedSearchImageUrls.contains(url);

                                return GestureDetector(
                                  onTap: () {
                                    setModalState(() {
                                      if (isSelected) {
                                        _selectedSearchImageUrls.remove(url);
                                      } else if (_selectedSearchImageUrls
                                              .length <
                                          5) {
                                        _selectedSearchImageUrls.add(url);
                                      } else {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                                'You can select up to 5 images'),
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                      }
                                    });
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: isSelected
                                            ? Colors.blue
                                            : Colors.grey[300]!,
                                        width: isSelected ? 3 : 1,
                                      ),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: Stack(
                                        children: [
                                          Image.network(
                                            url,
                                            fit: BoxFit.cover,
                                            width: double.infinity,
                                            height: double.infinity,
                                            loadingBuilder: (context, child,
                                                loadingProgress) {
                                              if (loadingProgress == null)
                                                return child;
                                              return Container(
                                                color: Colors.grey[100],
                                                child: const Center(
                                                  child:
                                                      CircularProgressIndicator(),
                                                ),
                                              );
                                            },
                                            errorBuilder:
                                                (context, error, stackTrace) =>
                                                    Container(
                                              color: Colors.grey[200],
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Icon(Icons.broken_image,
                                                      color: Colors.grey),
                                                  SizedBox(height: 4),
                                                  Text(
                                                    'Failed to load',
                                                    style: TextStyle(
                                                        fontSize: 10,
                                                        color: Colors.grey),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          if (isSelected)
                                            Positioned(
                                              top: 8,
                                              right: 8,
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.all(4),
                                                decoration: const BoxDecoration(
                                                  color: Colors.blue,
                                                  shape: BoxShape.circle,
                                                ),
                                                child: const Icon(
                                                  Icons.check,
                                                  color: Colors.white,
                                                  size: 16,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                ),

                // Bottom action button
                if (_selectedSearchImageUrls.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          blurRadius: 4,
                          offset: const Offset(0, -2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Text(
                          '${_selectedSearchImageUrls.length} image(s) selected',
                          style:
                              const TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () => _addSelectedImages(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'Add Selected Images',
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<void> _performImageSearch(StateSetter setModalState) async {
    if (_searchController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a search term'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    setModalState(() {
      _isSearchingImages = true;
    });

    try {
      final results =
          await _serperService.searchImages(_searchController.text.trim());
      setModalState(() {
        _searchedImageUrls.clear();
        _searchedImageUrls.addAll(results);
        _selectedSearchImageUrls
            .clear(); // Reset selection when new search is performed
      });
    } catch (e) {
      debugPrint('Error searching images: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to search images: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      setModalState(() {
        _isSearchingImages = false;
      });
    }
  }

  Future<List<String>> _uploadPostImages() async {
    if (_selectedImages.isEmpty) {
      return [];
    }

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    List<String> imageUrls = [];

    for (int i = 0; i < _selectedImages.length; i++) {
      final imageItem = _selectedImages[i];

      if (imageItem.isNetwork && imageItem.url != null) {
        // For network images, use the URL directly
        imageUrls.add(imageItem.url!);
      } else if (imageItem.file != null) {
        // For local files, upload to Firebase Storage as before
        final image = imageItem.file!;
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final ref = FirebaseStorage.instance
            .ref()
            .child('post_images')
            .child(user.uid)
            .child('${timestamp}_$i.jpg');

        for (int attempt = 1; attempt <= 3; attempt++) {
          try {
            UploadTask uploadTask;
            if (kIsWeb) {
              final bytes = await image.readAsBytes();
              uploadTask = ref.putData(bytes);
            } else {
              uploadTask = ref.putFile(File(image.path));
            }

            final snapshot =
                await uploadTask.timeout(const Duration(seconds: 120));
            if (snapshot.state == TaskState.success) {
              final downloadUrl = await snapshot.ref
                  .getDownloadURL()
                  .timeout(const Duration(seconds: 30));
              imageUrls.add(downloadUrl);
              break;
            } else {
              throw Exception('Upload failed with state: ${snapshot.state}');
            }
          } catch (e) {
            debugPrint('Error uploading image on attempt $attempt: $e');
            if (attempt == 3) {
              throw Exception(
                  'Failed to upload post image after 3 attempts: $e');
            }
            await Future.delayed(Duration(seconds: attempt * 2));
          }
        }
      }
    }
    return imageUrls;
  }

  Future<List<String>> _uploadPostVideos() async {
    if (_selectedVideos.isEmpty) {
      return [];
    }

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    List<String> videoUrls = [];

    for (int i = 0; i < _selectedVideos.length; i++) {
      final video = _selectedVideos[i];
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final ref = FirebaseStorage.instance
          .ref()
          .child('post_videos')
          .child(user.uid)
          .child('${timestamp}_$i.mp4');

      for (int attempt = 1; attempt <= 3; attempt++) {
        try {
          UploadTask uploadTask;
          if (kIsWeb) {
            final bytes = await video.readAsBytes();
            uploadTask = ref.putData(bytes);
          } else {
            uploadTask = ref.putFile(File(video.path));
          }

          final snapshot = await uploadTask.timeout(const Duration(minutes: 5));
          if (snapshot.state == TaskState.success) {
            final downloadUrl = await snapshot.ref
                .getDownloadURL()
                .timeout(const Duration(seconds: 30));
            videoUrls.add(downloadUrl);
            break;
          } else {
            throw Exception('Upload failed with state: ${snapshot.state}');
          }
        } catch (e) {
          debugPrint('Error uploading video on attempt $attempt: $e');
          if (attempt == 3) {
            throw Exception('Failed to upload post video after 3 attempts: $e');
          }
          await Future.delayed(Duration(seconds: attempt * 2));
        }
      }
    }
    return videoUrls;
  }

  Future<List<String>> _uploadVideoAds() async {
    if (_selectedVideoAds.isEmpty) {
      return [];
    }

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    List<String> videoAdUrls = [];

    for (int i = 0; i < _selectedVideoAds.length; i++) {
      final videoAd = _selectedVideoAds[i];
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final ref = FirebaseStorage.instance
          .ref()
          .child('video_ads')
          .child(user.uid)
          .child('${timestamp}_$i.mp4');

      for (int attempt = 1; attempt <= 3; attempt++) {
        try {
          UploadTask uploadTask;
          if (kIsWeb) {
            final bytes = await videoAd.readAsBytes();
            uploadTask = ref.putData(bytes);
          } else {
            uploadTask = ref.putFile(File(videoAd.path));
          }

          final snapshot = await uploadTask.timeout(const Duration(minutes: 5));
          if (snapshot.state == TaskState.success) {
            final downloadUrl = await snapshot.ref
                .getDownloadURL()
                .timeout(const Duration(seconds: 30));
            videoAdUrls.add(downloadUrl);
            break;
          } else {
            throw Exception('Upload failed with state: ${snapshot.state}');
          }
        } catch (e) {
          debugPrint('Error uploading video ad on attempt $attempt: $e');
          if (attempt == 3) {
            throw Exception('Failed to upload video ad after 3 attempts: $e');
          }
          await Future.delayed(Duration(seconds: attempt * 2));
        }
      }
    }
    return videoAdUrls;
  }

  Map<String, dynamic> _getCategoryData(String categoryName) {
    return _categories.firstWhere(
      (cat) => cat['name'] == categoryName,
      orElse: () => _categories[0],
    );
  }

  Future<void> _publishPost() async {
    if (_isSubmitting) return;

    if (_contentController.text.trim().isEmpty) {
      _showErrorMessage('Please write something to share');
      return;
    }

    if (_contentController.text.length > _maxCharacters) {
      _showErrorMessage('Post exceeds $_maxCharacters character limit');
      return;
    }

    final selectedCategory = _categoryController.selectedCategoryName;
    final currentBalance = walletController.balance;
    if (_postPrice > currentBalance) {
      _showErrorMessage(
        'Insufficient funds. Your balance: ${walletController.formatCurrency(currentBalance)}',
        showAddFunds: true,
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
      _isLoading = true;
    });

    try {
      debugPrint('Starting post creation process...');

      List<String> imageUrls = [];
      if (_selectedImages.isNotEmpty) {
        debugPrint('Uploading ${_selectedImages.length} images...');
        imageUrls = await _uploadPostImages();
        debugPrint('Images uploaded successfully: ${imageUrls.length} URLs');
      }

      List<String> videoUrls = [];
      if (_selectedVideos.isNotEmpty) {
        debugPrint('Uploading ${_selectedVideos.length} videos...');
        videoUrls = await _uploadPostVideos();
        debugPrint('Videos uploaded successfully: ${videoUrls.length} URLs');
      }

      List<String> videoAdUrls = [];
      if (_selectedVideoAds.isNotEmpty) {
        debugPrint('Uploading ${_selectedVideoAds.length} video ads...');
        videoAdUrls = await _uploadVideoAds();
        debugPrint(
            'Video ads uploaded successfully: ${videoAdUrls.length} URLs');
      }

      final allVideoUrls = [...videoUrls, ...videoAdUrls];

      debugPrint('Creating post in Firestore...');
      final postId = await _postController.createPost(
        content: _contentController.text.trim(),
        price: _postPrice,
        category: selectedCategory,
        tags: [],
        isPublic: true,
        allowComments: true,
        imageUrls: imageUrls,
        videoUrls: allVideoUrls,
        linkUrl: _linkUrl,
        hasPoll: _hasPoll,
      );
      debugPrint('Post created successfully with ID: $postId');

      debugPrint('Deducting wallet funds: \$$_postPrice');
      final balanceDeducted = await walletController.deductFunds(
        amount: _postPrice,
        description: 'Post creation in $selectedCategory',
        postId: postId,
      );

      if (!balanceDeducted) {
        throw Exception('Failed to process payment. Please try again.');
      }
      debugPrint('Wallet funds deducted successfully');

      // Clear form data before navigation
      debugPrint('Clearing form data...');
      _contentController.clear();
      _selectedImages.clear();
      _selectedVideos.clear();
      _selectedVideoAds.clear();
      _selectedSearchImageUrls.clear(); // Clear web images too
      _linkUrl = null;
      _draftController.clearAutoDraft();

      // Reset loading states before showing success feedback
      debugPrint('Resetting loading states...');
      if (mounted) {
        setState(() {
          _isSubmitting = false;
          _isLoading = false;
        });
      }

      if (mounted) {
        debugPrint(
            'Post creation completed successfully, showing success feedback...');
        HapticFeedback.mediumImpact();

        // Show success message with error handling
        try {
          debugPrint('Showing success SnackBar...');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Post published in $selectedCategory! Amount: ${walletController.formatCurrency(_postPrice)}',
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
          debugPrint('Success SnackBar shown successfully');
        } catch (e) {
          debugPrint('Error showing success SnackBar: $e');
        }

        // Navigate to home after a brief delay
        debugPrint('Waiting before navigation...');
        await Future.delayed(const Duration(milliseconds: 300));

        if (mounted) {
          try {
            debugPrint('Navigating to home screen...');
            Navigator.of(context)
                .pushNamedAndRemoveUntil('/home', (route) => false);
            debugPrint('Navigation to home completed');
          } catch (e) {
            debugPrint('Error navigating to home with Navigator: $e');
            // Try GetX navigation as fallback
            try {
              debugPrint('Trying GetX navigation...');
              Get.offAllNamed('/home');
              debugPrint('GetX navigation completed');
            } catch (getxError) {
              debugPrint('Error with GetX navigation: $getxError');
              // Final fallback - just pop
              if (mounted) {
                debugPrint('Using final fallback navigation (pop)...');
                Navigator.of(context).pop();
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _publishPost: $e');
      if (mounted) {
        setState(() {
          _isSubmitting = false;
          _isLoading = false;
        });

        // Use a safer way to show error message
        try {
          _showErrorMessage(
              'Failed to create post: ${e.toString().replaceAll('Exception: ', '')}');
        } catch (contextError) {
          debugPrint('Error showing error message: $contextError');
          // Fallback: just print the error if we can't show SnackBar
        }
      }
    }
  }

  void _showErrorMessage(String message, {bool showAddFunds = false}) {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: showAddFunds
              ? SnackBarAction(
                  label: 'ReUp!',
                  textColor: Colors.white,
                  onPressed: () {
                    if (mounted) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const WalletScreen()),
                      ).then((_) {
                        if (mounted) setState(() {});
                      });
                    }
                  },
                )
              : null,
        ),
      );
    } catch (e) {
      debugPrint('Error showing SnackBar: $e');
      // Fallback: just log the original message
      debugPrint('Original error message: $message');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final categoryData =
          _getCategoryData(_categoryController.selectedCategoryName);
      final categoryColor = categoryData['color'] as Color;

      return Scaffold(
        backgroundColor: Colors.grey[50],
        body: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Center(
                child: Container(
                  constraints:
                      BoxConstraints(maxWidth: kIsWeb ? 800 : double.infinity),
                  margin: kIsWeb
                      ? const EdgeInsets.symmetric(horizontal: 24)
                      : EdgeInsets.zero,
                  child: Column(
                    children: [
                      Container(
                        color: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 16),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Center(
                                    child: Container(
                                      width: 50,
                                      height: 50,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(25)),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(25),
                                        child: Image.asset(
                                          'assets/images/money_mouth.png',
                                          width: 50,
                                          height: 50,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(0),
                          child: _buildExpandedView(categoryColor),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );
    });
  }

  Widget _buildExpandedView(Color categoryColor) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 2),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                _categoryController.selectedCategoryName,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              constraints: BoxConstraints(minHeight: double.minPositive),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  if (_selectedImages.isNotEmpty ||
                      _selectedVideos.isNotEmpty ||
                      _selectedVideoAds.isNotEmpty ||
                      _linkUrl != null ||
                      _hasPoll) ...[
                    const SizedBox(height: 12),
                    _buildMediaPreview(),
                  ],
                  SizedBox(
                    height: 250,
                    child: TextFormField(
                      controller: _contentController,
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                      ),
                      style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                      maxLines: null,
                      textAlignVertical: TextAlignVertical.top,
                      onChanged: (value) {
                        setState(() {});
                        _draftController.startAutoSave(
                          content: value,
                          price: _postPrice,
                          category: _categoryController.selectedCategoryName,
                          isPublic: true,
                          allowComments: true,
                          linkUrl: _linkUrl,
                          images: _selectedImages
                              .where((item) => item.file != null)
                              .map((item) => item.file!)
                              .toList(),
                          videos: _selectedVideos,
                          hasPoll: _hasPoll,
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildMediaButtonsRow(),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_contentController.text.length}/$_maxCharacters characters',
                  style: TextStyle(
                    fontSize: 12,
                    color: _contentController.text.length > _maxCharacters
                        ? Colors.red
                        : Colors.grey[500],
                  ),
                ),
                Text(
                  'Cost: ${walletController.formatCurrency(_postPrice)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: categoryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(
              width: double.infinity,
              height: 35,
              child: ElevatedButton(
                onPressed: (_isLoading || _isSubmitting) ? null : _publishPost,
                style: ElevatedButton.styleFrom(
                  backgroundColor: categoryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12)),
                  elevation: 0,
                ),
                child: (_isLoading || _isSubmitting)
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                            color: Colors.white, strokeWidth: 2),
                      )
                    : const Text(
                        'Put Up',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w600),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaPreview() {
    return SizedBox(
      height: 50,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            ..._selectedImages.asMap().entries.map((entry) {
              final index = entry.key;
              final imageItem = entry.value;
              return Container(
                width: 40,
                height: 40,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: imageItem.isNetwork && imageItem.url != null
                          ? Image.network(
                              imageItem.url!,
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return Container(
                                  width: 40,
                                  height: 40,
                                  color: Colors.grey[200],
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2),
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                width: 40,
                                height: 40,
                                color: Colors.grey[200],
                                child: const Icon(Icons.broken_image, size: 20),
                              ),
                            )
                          : imageItem.file != null
                              ? (kIsWeb
                                  ? FutureBuilder<Uint8List>(
                                      future: imageItem.file!.readAsBytes(),
                                      builder: (context, snapshot) {
                                        if (snapshot.hasData) {
                                          return Image.memory(
                                            snapshot.data!,
                                            width: 40,
                                            height: 40,
                                            fit: BoxFit.cover,
                                          );
                                        }
                                        return Container(
                                          width: 40,
                                          height: 40,
                                          color: Colors.grey[200],
                                          child:
                                              const Icon(Icons.image, size: 20),
                                        );
                                      },
                                    )
                                  : Image.file(
                                      File(imageItem.file!.path),
                                      width: 40,
                                      height: 40,
                                      fit: BoxFit.cover,
                                    ))
                              : Container(
                                  width: 40,
                                  height: 40,
                                  color: Colors.grey[200],
                                  child: const Icon(Icons.image, size: 20),
                                ),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: () => _removeImage(index),
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
            ..._selectedVideos.asMap().entries.map((entry) {
              final index = entry.key;
              return Container(
                width: 40,
                height: 40,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.videocam, size: 16, color: Colors.blue),
                          SizedBox(height: 2),
                          Text('Video',
                              style:
                                  TextStyle(fontSize: 8, color: Colors.blue)),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: () => _removeVideo(index),
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
            ..._selectedVideoAds.asMap().entries.map((entry) {
              final index = entry.key;
              return Container(
                width: 40,
                height: 40,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[300]!),
                  color: Colors.orange[50],
                ),
                child: Stack(
                  children: [
                    const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.ads_click, size: 16, color: Colors.orange),
                          SizedBox(height: 2),
                          Text(
                            'Ad',
                            style: TextStyle(
                                fontSize: 8,
                                color: Colors.orange,
                                fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: () => _removeVideoAd(index),
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
            if (_linkUrl != null)
              Container(
                width: 40,
                height: 40,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[300]!),
                  color: Colors.blue[50],
                ),
                child: Stack(
                  children: [
                    const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.link, size: 16, color: Colors.blue),
                          SizedBox(height: 2),
                          Text('Link',
                              style:
                                  TextStyle(fontSize: 8, color: Colors.blue)),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: _removeLink,
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            if (_hasPoll)
              Container(
                width: 40,
                height: 40,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[300]!),
                  color: Colors.green[50],
                ),
                child: Stack(
                  children: [
                    const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.poll, size: 16, color: Colors.green),
                          SizedBox(height: 2),
                          Text('Poll',
                              style:
                                  TextStyle(fontSize: 8, color: Colors.green)),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: () => setState(() => _hasPoll = false),
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaButtonsRow() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      alignment: WrapAlignment.start,
      direction: Axis.horizontal,
      runAlignment: WrapAlignment.start,
      children: [
        _buildMediaButton(
          icon: Icons.photo_library,
          label: 'Photo',
          onTap: _pickImage,
          color: Colors.purple,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.camera_alt,
          label: 'Camera',
          onTap: _takePhoto,
          color: Colors.teal,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.search,
          label: 'Search Images',
          onTap: _showImageSearchBottomSheet,
          color: Colors.blueGrey,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.videocam,
          label: _selectedVideos.isNotEmpty ? 'Video(1/1)' : 'Video(30)',
          onTap: _selectedVideos.isNotEmpty ? null : _showVideoOptions,
          color: Colors.indigo,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.ads_click,
          label:
              _selectedVideoAds.isNotEmpty ? 'Video Ad(1/1)' : 'Video Ad(60)',
          onTap: _selectedVideoAds.isNotEmpty ? null : _showVideoAdOptions,
          color: Colors.orange,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.link,
          label: 'Link',
          onTap: _showAddLinkDialog,
          color: Colors.blue,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.emoji_emotions,
          label: 'Emoji',
          onTap: _showEmojiPicker,
          color: Colors.pink,
          textColor: Colors.white,
        ),
        _buildMediaButton(
          icon: Icons.poll,
          label: _hasPoll ? 'Poll ✓' : 'Poll',
          onTap: _togglePoll,
          color: Colors.green,
          textColor: Colors.white,
        ),
      ],
    );
  }

  Widget _buildMediaButton({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
    Color? color,
    Color? textColor,
  }) {
    final isDisabled = onTap == null;
    final buttonColor = color ?? Colors.grey[400]!;
    final finalTextColor = textColor ?? Colors.grey[700]!;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color: isDisabled ? Colors.grey[200] : buttonColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: isDisabled
              ? null
              : [
                  BoxShadow(
                    color: buttonColor.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 14,
              color: isDisabled ? Colors.grey[400] : finalTextColor,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isDisabled ? Colors.grey[400] : finalTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showEmojiPicker() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 250,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Select Emoji',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: GridView.count(
                crossAxisCount: 8,
                children: [
                  '😀',
                  '😃',
                  '😄',
                  '😁',
                  '😆',
                  '😅',
                  '😂',
                  '🤣',
                  '😊',
                  '😇',
                  '🙂',
                  '🙃',
                  '😉',
                  '😌',
                  '😍',
                  '🥰',
                  '😘',
                  '😗',
                  '😙',
                  '😚',
                  '😋',
                  '😛',
                  '😝',
                  '😜',
                  '🤪',
                  '🤨',
                  '🧐',
                  '🤓',
                  '😎',
                  '🤩',
                  '🥳',
                  '😏',
                  '😒',
                  '😞',
                  '😔',
                  '😟',
                  '😕',
                  '🙁',
                  '☹️',
                  '😣',
                  '😖',
                  '😫',
                  '😩',
                  '🥺',
                  '😢',
                  '😭',
                  '😤',
                  '😠',
                  '😡',
                  '🤬',
                  '🤯',
                  '😳',
                  '🥵',
                  '🥶',
                  '😱',
                  '😨',
                  '😰',
                  '😥',
                  '😓',
                  '🤗',
                  '🤔',
                  '🤭',
                  '🤫',
                  '🤥'
                ]
                    .map((emoji) => GestureDetector(
                          onTap: () {
                            _insertEmoji(emoji);
                            Navigator.pop(context);
                          },
                          child: Container(
                            margin: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.grey[100],
                            ),
                            child: Center(
                                child: Text(emoji,
                                    style: const TextStyle(fontSize: 20))),
                          ),
                        ))
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _togglePoll() {
    setState(() {
      _hasPoll = !_hasPoll;
    });
  }

  void _showVideoOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Add Video (30s)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.videocam, color: Colors.blue),
              title: const Text('Upload from Gallery'),
              subtitle: const Text('Choose existing video'),
              onTap: () {
                Navigator.pop(context);
                _pickVideo();
              },
            ),
            ListTile(
              leading: const Icon(Icons.video_camera_back, color: Colors.red),
              title: const Text('Record Video'),
              subtitle: const Text('Record new video'),
              onTap: () {
                Navigator.pop(context);
                _recordVideo();
              },
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  void _showVideoAdOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Add Video Ad (60s)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Price will be set to Top Paid Post + \$1',
              style: TextStyle(
                  fontSize: 14,
                  color: Colors.orange,
                  fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.ads_click, color: Colors.orange),
              title: const Text('Upload from Gallery'),
              subtitle: const Text('Choose existing video ad'),
              onTap: () {
                Navigator.pop(context);
                _pickVideoAd();
              },
            ),
            ListTile(
              leading: const Icon(Icons.video_camera_back, color: Colors.red),
              title: const Text('Record Video Ad'),
              subtitle: const Text('Record new video ad'),
              onTap: () {
                Navigator.pop(context);
                _recordVideoAd();
              },
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}
