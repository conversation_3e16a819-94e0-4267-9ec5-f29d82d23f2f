import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:web/web.dart' as web;
import 'dart:html' as html;

class PaymentRedirectHandler {
  static const String _baseUrl =
      'https://us-central1-money-mouthy.cloudfunctions.net';

  // Track processed payment intents to prevent duplicate processing
  static final Set<String> _processedPaymentIntents = <String>{};

  /// Check if the current URL contains payment redirect parameters
  static bool hasPaymentRedirect() {
    if (!kIsWeb) return false;

    final url = web.window.location.href;
    return url.contains('payment_intent=') ||
        url.contains('payment_modal=true');
  }

  /// Check if a payment intent has already been processed
  static bool _isPaymentIntentProcessed(String paymentIntentId) {
    return _processedPaymentIntents.contains(paymentIntentId);
  }

  /// Mark a payment intent as processed
  static void _markPaymentIntentAsProcessed(String paymentIntentId) {
    _processedPaymentIntents.add(paymentIntentId);
  }

  /// Clear all processed payment intents (useful for logout or reset)
  static void clearProcessedPaymentIntents() {
    _processedPaymentIntents.clear();
  }

  /// Force clean the URL (public method for external use)
  static void forceCleanUrl() {
    _cleanUrl();
  }

  /// Extract payment intent ID from URL parameters
  static String? getPaymentIntentFromUrl() {
    if (!kIsWeb) return null;

    final url = web.window.location.href;
    final uri = Uri.parse(url);

    return uri.queryParameters['payment_intent'];
  }

  /// Get redirect status from URL
  static String? getRedirectStatus() {
    if (!kIsWeb) return null;

    final url = web.window.location.href;
    final uri = Uri.parse(url);

    return uri.queryParameters['redirect_status'];
  }

  /// Check if this is a payment modal redirect
  static bool isPaymentModalRedirect() {
    if (!kIsWeb) return false;

    final url = web.window.location.href;
    final uri = Uri.parse(url);

    return uri.queryParameters['payment_modal'] == 'true';
  }

  /// Handle payment redirect and process the result
  static Future<PaymentRedirectResult?> handlePaymentRedirect(
      BuildContext context) async {
    try {
      final paymentIntentId = getPaymentIntentFromUrl();
      final redirectStatus = getRedirectStatus();

      if (paymentIntentId == null) {
        return null;
      }

      // Check if this payment intent has already been processed
      if (_isPaymentIntentProcessed(paymentIntentId)) {
        // Return null to indicate that the payment has already been processed
        return null;
      }

      // Mark as processed to prevent duplicate processing
      _markPaymentIntentAsProcessed(paymentIntentId);

      if (redirectStatus == 'succeeded') {
        // Process successful payment
        final success = await _processPaymentCompletion(paymentIntentId);
        if (success) {
          return PaymentRedirectResult.success('Payment completed');
        } else {
          return PaymentRedirectResult.error('Failed to process payment');
        }
      } else if (redirectStatus == 'failed') {
        return PaymentRedirectResult.error('Payment failed');
      } else if (redirectStatus == 'canceled') {
        return PaymentRedirectResult.cancelled('Payment was cancelled');
      } else {
        return PaymentRedirectResult.error(
            'Unknown payment status: $redirectStatus');
      }
    } catch (e) {
      return PaymentRedirectResult.error('Error handling payment redirect: $e');
    } finally {
      _cleanUrl();
    }
  }

  /// Process payment completion by calling our Firebase Function
  static Future<bool> _processPaymentCompletion(String paymentIntentId) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/handlePaymentCompletion'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'paymentIntentId': paymentIntentId,
          'userId': user.uid,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      } else {
        debugPrint(
            'Payment completion failed: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Error processing payment completion: $e');
      return false;
    }
  }

  /// Clean the URL
  static void _cleanUrl() {
    if (!kIsWeb) return;

    try {
      final baseUri = Uri.base.removeFragment().replace(queryParameters: {});
      html.window.history.replaceState(null, '', baseUri.toString());
    } catch (e) {
      debugPrint('Error cleaning URL: $e');
    }
  }

  /// Show appropriate message based on payment result
  static void showPaymentResult(
      BuildContext context, PaymentRedirectResult result) {
    // Don't show message for already processed payments
    if (result.isSuccess && result.message == 'Payment already processed') {
      return;
    }

    Color backgroundColor;
    IconData icon;

    switch (result.type) {
      case PaymentRedirectType.success:
        backgroundColor = Colors.green;
        icon = Icons.check_circle;
        break;
      case PaymentRedirectType.error:
        backgroundColor = Colors.red;
        icon = Icons.error;
        break;
      case PaymentRedirectType.cancelled:
        backgroundColor = Colors.orange;
        icon = Icons.cancel;
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(result.message)),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 4),
      ),
    );
  }
}

/// Result of payment redirect handling
class PaymentRedirectResult {
  final PaymentRedirectType type;
  final String message;

  const PaymentRedirectResult._(this.type, this.message);

  factory PaymentRedirectResult.success(String message) =>
      PaymentRedirectResult._(PaymentRedirectType.success, message);

  factory PaymentRedirectResult.error(String message) =>
      PaymentRedirectResult._(PaymentRedirectType.error, message);

  factory PaymentRedirectResult.cancelled(String message) =>
      PaymentRedirectResult._(PaymentRedirectType.cancelled, message);

  bool get isSuccess => type == PaymentRedirectType.success;
  bool get isError => type == PaymentRedirectType.error;
  bool get isCancelled => type == PaymentRedirectType.cancelled;
}

enum PaymentRedirectType {
  success,
  error,
  cancelled,
}
