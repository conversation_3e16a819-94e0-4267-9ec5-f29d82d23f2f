import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:moneymouthy/services/logout_service.dart';

/// Authentication middleware for protecting main app routes
class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final user = FirebaseAuth.instance.currentUser;

    // Check if user is authenticated
    if (user == null) {
      debugPrint(
          'AuthMiddleware: User not authenticated, redirecting to login');

      // Redirect to appropriate login screen based on platform
      if (kIsWeb) {
        return const RouteSettings(name: '/landing');
      } else {
        return const RouteSettings(name: '/login');
      }
    }

    // User is authenticated, allow access
    debugPrint('AuthMiddleware: User authenticated, allowing access to $route');
    return null;
  }

  @override
  GetPage? onPageCalled(GetPage? page) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      debugPrint(
          'AuthMiddleware: Blocking page access - user not authenticated');

      // Return login page instead
      if (kIsWeb) {
        return GetPage(
          name: '/landing',
          page: () => Container(
            color: Colors.white,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
        );
      } else {
        return GetPage(
          name: '/login',
          page: () => Container(
            color: Colors.white,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
        );
      }
    }

    return page;
  }
}

/// Middleware specifically for protecting the home route and main navigation
class HomeAuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      debugPrint(
          'HomeAuthMiddleware: User not authenticated, redirecting from home');

      if (kIsWeb) {
        return const RouteSettings(name: '/landing');
      } else {
        return const RouteSettings(name: '/login');
      }
    }

    return null;
  }
}

/// Middleware for protecting wallet-related routes
class WalletAuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      debugPrint(
          'WalletAuthMiddleware: User not authenticated, blocking wallet access');

      if (kIsWeb) {
        return const RouteSettings(name: '/landing');
      } else {
        return const RouteSettings(name: '/login');
      }
    }

    return null;
  }
}

/// Middleware for protecting post creation and management routes
class PostAuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      debugPrint(
          'PostAuthMiddleware: User not authenticated, blocking post access');

      if (kIsWeb) {
        return const RouteSettings(name: '/landing');
      } else {
        return const RouteSettings(name: '/login');
      }
    }

    return null;
  }
}

/// Middleware for protecting profile and user-related routes
class ProfileAuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      debugPrint(
          'ProfileAuthMiddleware: User not authenticated, blocking profile access');

      if (kIsWeb) {
        return const RouteSettings(name: '/landing');
      } else {
        return const RouteSettings(name: '/login');
      }
    }

    return null;
  }
}

/// Utility class for checking authentication status
class AuthGuard {
  /// Check if user is currently authenticated
  static bool isAuthenticated() {
    return FirebaseAuth.instance.currentUser != null;
  }

  /// Get current user ID (null if not authenticated)
  static String? getCurrentUserId() {
    return FirebaseAuth.instance.currentUser?.uid;
  }

  /// Check if user is authenticated and show error if not
  static bool requireAuthentication(BuildContext context) {
    if (!isAuthenticated()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please log in to access this feature'),
          backgroundColor: Colors.red,
        ),
      );

      // Navigate to login
      if (kIsWeb) {
        Navigator.of(context).pushReplacementNamed('/landing');
      } else {
        Navigator.of(context).pushReplacementNamed('/login');
      }

      return false;
    }

    return true;
  }

  /// Wrapper for protected actions that require authentication
  static Future<T?> withAuth<T>(
    BuildContext context,
    Future<T> Function() action,
  ) async {
    if (!requireAuthentication(context)) {
      return null;
    }

    return await action();
  }
}
