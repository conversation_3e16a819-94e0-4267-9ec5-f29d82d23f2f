import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

import '../../widgets/payments/payment_modal_web.dart';

/// Simple Stripe service for payment processing using Payment Sheet
class StripeService {
  // Direct Cloud Function URL
  static const String _createPaymentIntentUrl =
      'https://us-central1-money-mouthy.cloudfunctions.net/createPaymentIntent';

  /// Create payment intent on backend
  static Future<Map<String, dynamic>?> _createPaymentIntent(
    double amount,
  ) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get Firebase ID token for authentication
      final idToken = await user.getIdToken();

      final response = await http.post(
        Uri.parse(_createPaymentIntentUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $idToken',
        },
        body: jsonEncode({
          'amount': (amount * 100).round(),
          'currency': 'usd',
          'email': user.email,
        }),
      );

      if (response.statusCode != 200) {
        final errorData = jsonDecode(response.body);
        throw Exception(
          'HTTP ${response.statusCode}: ${errorData['error'] ?? 'Unknown error'}',
        );
      }

      final responseData = jsonDecode(response.body);

      // Debug: Print the actual response structure
      debugPrint('Payment Intent Response: $responseData');

      // Handle different possible response structures
      Map<String, dynamic>? result;
      if (responseData.containsKey('result') &&
          responseData['result'] != null) {
        result = responseData['result'] as Map<String, dynamic>;
      } else if (responseData is Map<String, dynamic>) {
        // If no 'result' wrapper, use the response data directly
        result = responseData;
      }

      // Validate required fields exist
      if (result == null) {
        throw Exception('Invalid response: result is null');
      }

      if (!result.containsKey('ephemeralKey') ||
          !result.containsKey('paymentIntent')) {
        throw Exception(
          'Invalid response: missing required fields. Response: $responseData',
        );
      }

      return {
        'ephemeralKey': result['ephemeralKey'],
        'paymentIntent': result['paymentIntent'],
        'customerEmail': result['customerEmail'] ?? result['customer_email'],
        'customerId': result['customer'] ?? result['customerId'],
      };
    } catch (e) {
      debugPrint('Error creating payment intent: $e');

      if (e.toString().contains('HTTP 401') ||
          e.toString().contains('Unauthorized')) {
        throw Exception('Authentication failed. Please sign in again.');
      }

      throw Exception('Failed to create payment intent: $e');
    }
  }

  /// Process payment using Stripe Payment Sheet (mobile) or Payment Element (web)
  static Future<bool> processPayment(double amount,
      [BuildContext? context]) async {
    try {
      // Create payment intent
      final paymentData = await _createPaymentIntent(amount);
      if (paymentData == null) {
        throw Exception('Failed to create payment intent, but HTTP ok');
      }

      if (kIsWeb && context != null) {
        // Web payment using Payment Element
        return await _processWebPayment(
            paymentData['paymentIntent'], amount, context);
      } else {
        // Mobile payment using Payment Sheet
        return await _processMobilePayment(paymentData);
      }
    } on StripeException catch (e) {
      debugPrint('Stripe error: ${e.error.localizedMessage}');

      // Re-throw cancellation errors so they can be handled appropriately
      if (e.error.code == FailureCode.Canceled) {
        throw Exception('Payment was cancelled');
      }

      throw Exception(e.error.localizedMessage ?? 'Payment failed');
    } catch (e) {
      debugPrint('Payment error: $e');
      rethrow;
    }
  }

  /// Process mobile payment using Payment Sheet
  static Future<bool> _processMobilePayment(
      Map<String, dynamic> paymentData) async {
    // Initialize payment sheet
    await Stripe.instance.initPaymentSheet(
      paymentSheetParameters: SetupPaymentSheetParameters(
        paymentIntentClientSecret: paymentData['paymentIntent'],
        customerEphemeralKeySecret: paymentData['ephemeralKey'],
        merchantDisplayName: 'Money Mouthy',
        customerId: paymentData['customer'],
        style: ThemeMode.system,
        billingDetails: BillingDetails(
          email: paymentData['customerEmail'],
          name: FirebaseAuth.instance.currentUser?.displayName,
        ),
      ),
    );

    // Present payment sheet
    await Stripe.instance.presentPaymentSheet();
    debugPrint('Mobile payment successful');
    return true;
  }

  /// Process web payment using Payment Modal
  static Future<bool> _processWebPayment(
      String clientSecret, double amount, BuildContext? context) async {
    if (kIsWeb && context != null) {
      // Show payment modal instead of navigating to a new screen
      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => PaymentModalWeb(
          amount: amount,
          clientSecret: clientSecret,
          onSuccess: () {
            // Modal will handle its own closing
          },
          onCancel: () {
            Navigator.of(context).pop(false);
          },
        ),
      );
      return result ?? false;
    }
    return false;
  }
}
