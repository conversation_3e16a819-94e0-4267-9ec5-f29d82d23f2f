<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Serper API Image Search Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .header h1 {
            color: #1a202c;
            margin-bottom: 12px;
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #718096;
            font-size: 1.2rem;
            font-weight: 500;
        }

        .search-section {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            padding: 35px;
            border-radius: 20px;
            margin-bottom: 40px;
            border: 1px solid #e2e8f0;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
        }

        .api-info {
            background: linear-gradient(135deg, #ebf8ff 0%, #bee3f8 100%);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #90cdf4;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
        }

        .api-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .api-info strong {
            color: #2b6cb0;
            font-weight: 700;
        }

        .api-info code {
            background: rgba(255, 255, 255, 0.8);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
            font-size: 0.9rem;
            color: #2d3748;
            border: 1px solid rgba(45, 55, 72, 0.1);
        }

        .search-form {
            display: grid;
            grid-template-columns: 2fr 1fr 2fr auto;
            gap: 20px;
            align-items: end;
        }

        .input-group {
            display: flex;
            flex-direction: column;
        }

        .input-group label {
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
            font-size: 0.95rem;
        }

        .input-group input,
        .input-group select {
            padding: 14px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            color: #2d3748;
        }

        .input-group input:focus,
        .input-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .search-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            height: fit-content;
        }

        .search-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .search-btn:active {
            transform: translateY(0);
        }

        .search-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            text-align: center;
            padding: 60px 20px;
            color: #667eea;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f1f5f9;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 25px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .results {
            margin-top: 40px;
        }

        .results h2 {
            color: #1a202c;
            margin-bottom: 25px;
            font-size: 2rem;
            font-weight: 700;
        }

        .stats {
            background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid #9ae6b4;
            font-size: 1.1rem;
        }

        .stats span {
            font-weight: 700;
            color: #2f855a;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .image-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #f7fafc;
            position: relative;
        }

        .image-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .image-wrapper {
            position: relative;
            overflow: hidden;
            height: 220px;
        }

        .image-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .image-card:hover img {
            transform: scale(1.1);
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .image-card:hover .image-overlay {
            opacity: 1;
        }

        .image-info {
            padding: 20px;
        }

        .image-title {
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 10px;
            font-size: 1rem;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            min-height: 48px;
        }

        .image-source {
            color: #718096;
            font-size: 0.9rem;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .image-actions {
            display: flex;
            gap: 10px;
        }

        .image-link {
            flex: 1;
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 0.85rem;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }

        .image-link:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
            transform: translateY(-1px);
        }

        .error {
            background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
            color: #c53030;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #fc8181;
            margin-top: 20px;
            font-weight: 500;
        }

        .welcome-message {
            text-align: center;
            padding: 60px 40px;
            color: #718096;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 16px;
            border: 2px dashed #cbd5e0;
        }

        .welcome-message h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .welcome-message p {
            font-size: 1.1rem;
        }

        .welcome-message a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .welcome-message a:hover {
            text-decoration: underline;
        }

        .api-key-hint {
            font-size: 0.85rem;
            color: #718096;
            margin-top: 5px;
            font-style: italic;
        }

        @media (max-width: 1024px) {
            .search-form {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .search-btn {
                justify-self: stretch;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 25px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2.2rem;
            }

            .image-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 20px;
            }

            .search-section {
                padding: 25px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Serper API Demo</h1>
            <p>Powerful Google Image Search Integration</p>
        </div>

        <div class="search-section">
            <div class="api-info">
                <div><strong>API Endpoint:</strong> <code>https://google.serper.dev/images</code></div>
                <div style="margin-top: 8px;"><strong>Method:</strong> <code>POST</code> with <code>X-API-KEY</code>
                    header</div>
            </div>

            <form class="search-form" id="searchForm">
                <div class="input-group">
                    <label for="query">Search Query</label>
                    <input type="text" id="query" placeholder="Enter your search query..."
                        value="Russia and USA ships starting war?" required>
                </div>

                <div class="input-group">
                    <label for="location">Location</label>
                    <select id="location">
                        <option value="United States">United States</option>
                        <option value="United Kingdom">United Kingdom</option>
                        <option value="Canada">Canada</option>
                        <option value="Australia">Australia</option>
                        <option value="Germany">Germany</option>
                        <option value="France">France</option>
                        <option value="Japan">Japan</option>
                        <option value="India">India</option>
                        <option value="Brazil">Brazil</option>
                    </select>
                </div>

                <div class="input-group">
                    <label for="apiKey">API Key</label>
                    <input type="password" id="apiKey" value="9899f6d13a966b030a9e8e3e1cce48fcf490e5d5" placeholder="Your Serper API key..." required>
                    <div class="api-key-hint">Get free API key at serper.dev</div>
                </div>

                <button type="submit" class="search-btn" id="searchBtn">
                    Search Images
                </button>
            </form>
        </div>

        <div id="loadingDiv" class="loading" style="display: none;">
            <div class="spinner"></div>
            <p><strong>Searching for images...</strong></p>
            <p style="margin-top: 10px; font-size: 0.9rem; opacity: 0.8;">This may take a few seconds</p>
        </div>

        <div id="resultsDiv" class="results">
            <div class="welcome-message">
                <h3>🚀 Ready to Search Images?</h3>
                <p>Enter your Serper API key above and click "Search Images" to get started.</p>
                <p style="margin-top: 10px;">Get your free API key at <a href="https://serper.dev"
                        target="_blank">serper.dev</a></p>
            </div>
        </div>

        <div id="errorDiv" class="error" style="display: none;"></div>
    </div>

    <script>
        let currentData = null;

        async function searchImages(query, location, apiKey) {
            const url = 'https://google.serper.dev/images';

            const requestBody = {
                q: query,
                location: location,
                num: 500,
                type: 'images'
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'X-API-KEY': apiKey,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API Error (${response.status}): ${errorText || 'Request failed'}`);
            }

            return await response.json();
        }

        function displayResults(data) {
            const resultsDiv = document.getElementById('resultsDiv');

            // Show stats
            const imageCount = data.images ? data.images.length : 0;
            const query = data.searchParameters?.q || 'unknown query';

            let html = `
                <h2>Search Results</h2>
                <div class="stats">
                    Found <span>${imageCount}</span> images for "<span>${query}</span>"
                </div>
            `;

            if (data.images && data.images.length > 0) {
                html += '<div class="image-grid">';

                data.images.forEach(image => {
                    const imageUrl =  image.imageUrl || image.image || image.thumbnail;
                    const title = image.title || 'Untitled';
                    const source = image.source || image.domain || 'Unknown source';
                    const link = image.link || '#';
                    const fallbackImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjIyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjdmYWZjIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5YTNhZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+SW1hZ2UgTm90IEF2YWlsYWJsZTwvdGV4dD48L3N2Zz4=';

                    html += `
                        <div class="image-card">
                            <div class="image-wrapper">
                                <img src="${imageUrl}" alt="${title}" onerror="this.src='${fallbackImage}'">
                                <div class="image-overlay"></div>
                            </div>
                            <div class="image-info">
                                <div class="image-title">${title}</div>
                                <div class="image-source">📌 ${source}</div>
                                <div class="image-actions">
                                    <a href="${link}" target="_blank" class="image-link">View Original</a>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += '</div>';
            } else {
                html += `
                    <div class="welcome-message">
                        <h3>No Images Found</h3>
                        <p>Try a different search query or check your API key.</p>
                    </div>
                `;
            }

            resultsDiv.innerHTML = html;
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorDiv');
            errorDiv.innerHTML = `<strong>Error:</strong> ${message}`;
            errorDiv.style.display = 'block';

            // Auto-hide error after 8 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 8000);
        }

        function hideError() {
            const errorDiv = document.getElementById('errorDiv');
            errorDiv.style.display = 'none';
        }

        // Form submission handler
        document.getElementById('searchForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            const query = document.getElementById('query').value.trim();
            const location = document.getElementById('location').value;
            const apiKey = document.getElementById('apiKey').value.trim();

            if (!query) {
                showError('Please enter a search query');
                return;
            }

            if (!apiKey) {
                showError('Please enter your Serper API key. Get one free at serper.dev');
                return;
            }

            const loadingDiv = document.getElementById('loadingDiv');
            const resultsDiv = document.getElementById('resultsDiv');
            const searchBtn = document.getElementById('searchBtn');

            // Show loading state
            hideError();
            loadingDiv.style.display = 'block';
            resultsDiv.style.display = 'none';
            searchBtn.disabled = true;
            searchBtn.textContent = 'Searching...';

            try {
                const data = await searchImages(query, location, apiKey);
                currentData = data;

                // Hide loading and show results
                loadingDiv.style.display = 'none';
                resultsDiv.style.display = 'block';
                displayResults(data);

            } catch (error) {
                loadingDiv.style.display = 'none';
                resultsDiv.style.display = 'block';

                // Show welcome message again
                document.getElementById('resultsDiv').innerHTML = `
                    <div class="welcome-message">
                        <h3>🚀 Ready to Search Images?</h3>
                        <p>Enter your Serper API key above and click "Search Images" to get started.</p>
                        <p style="margin-top: 10px;">Get your free API key at <a href="https://serper.dev" target="_blank">serper.dev</a></p>
                    </div>
                `;

                showError(error.message);
            } finally {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search Images';
            }
        });

        // Auto-focus on API key field when query is filled
        document.getElementById('query').addEventListener('input', function () {
            if (this.value && !document.getElementById('apiKey').value) {
                setTimeout(() => {
                    document.getElementById('apiKey').focus();
                }, 100);
            }
        });
    </script>
</body>

</html>